import React from 'react'

interface WindowTopBarProps {
  className?: string
}

const WindowTopBar: React.FC<WindowTopBarProps> = ({ className = '' }) => {
  const handleMinimize = () => {
    if (window.electronAPI?.windowControls) {
      window.electronAPI.windowControls.minimize()
    }
  }

  const handleMaximize = () => {
    if (window.electronAPI?.windowControls) {
      window.electronAPI.windowControls.maximize()
    }
  }

  const handleClose = () => {
    if (window.electronAPI?.windowControls) {
      window.electronAPI.windowControls.close()
    }
  }

  return (
    <div
      className={`h-6 glass-subtle flex items-center justify-between px-2 ${className}`}
      style={{ WebkitAppRegion: 'drag' } as React.CSSProperties}
    >
      {/* Empty left side for dragging */}
      <div className="flex-1"></div>

      {/* Window Controls */}
      <div
        className="flex items-center gap-2"
        style={{ WebkitAppRegion: 'no-drag' } as React.CSSProperties}
      >
        <button
          onClick={handleMinimize}
          className="w-5 h-5 flex items-center justify-center hover:bg-white/10 rounded transition-colors"
          title="Minimize"
        >
          <i className="fa-solid fa-minus text-gray-300 text-xs"></i>
        </button>
        <button
          onClick={handleMaximize}
          className="w-5 h-5 flex items-center justify-center hover:bg-white/10 rounded transition-colors"
          title="Maximize/Restore"
        >
          <i className="fa-solid fa-expand text-gray-300 text-xs"></i>
        </button>
        <button
          onClick={handleClose}
          className="w-5 h-5 flex items-center justify-center hover:bg-red-500/20 rounded transition-colors"
          title="Close"
        >
          <i className="fa-solid fa-xmark text-gray-300 hover:text-white text-xs"></i>
        </button>
      </div>
    </div>
  )
}

export default WindowTopBar
