# Homepage & Files UI Implementation Plan

## Executive Summary
This plan maps the homepage and files UI designs to the existing ChatLo architecture, introducing a **Context Vault** system that organizes conversations, files, and artifacts into project-based contexts while maintaining seamless integration with the current chat and history functionality.

## Design Analysis

### Homepage UI Design Features
- **Context Cards Grid**: Project-based organization with visual cards showing chat/file counts
- **Search & Filtering**: Global search across contexts with view toggles (grid/list)
- **Context Details Modal**: 3-panel view (Recent Chats 30% | Files 30% | Master.md Preview 40%)
- **VSCode-style Navigation**: Icon bar with Home, Chat, History, Files tabs

### Files UI Design Features  
- **File Tree Panel**: Hierarchical folder structure (20% width)
- **Markdown Preview**: Live preview of selected files (60% height)
- **Quick Actions Panel**: AI-powered file operations (right sidebar)
- **Recent Chats Integration**: Context-aware chat history (40% height)

## Feature Mapping & Data Flow

### 1. Context Vault System

#### New Database Schema Extensions
```sql
-- Contexts table (new)
CREATE TABLE contexts (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  icon TEXT DEFAULT 'folder',
  color TEXT DEFAULT 'primary',
  created_at TEXT NOT NULL,
  updated_at TEXT NOT NULL,
  is_pinned INTEGER DEFAULT 0
);

-- Context relationships (new)
CREATE TABLE context_conversations (
  id TEXT PRIMARY KEY,
  context_id TEXT NOT NULL,
  conversation_id TEXT NOT NULL,
  added_at TEXT NOT NULL,
  FOREIGN KEY (context_id) REFERENCES contexts (id) ON DELETE CASCADE,
  FOREIGN KEY (conversation_id) REFERENCES conversations (id) ON DELETE CASCADE
);

CREATE TABLE context_files (
  id TEXT PRIMARY KEY,
  context_id TEXT NOT NULL,
  file_id TEXT NOT NULL,
  added_at TEXT NOT NULL,
  FOREIGN KEY (context_id) REFERENCES contexts (id) ON DELETE CASCADE,
  FOREIGN KEY (file_id) REFERENCES files (id) ON DELETE CASCADE
);

-- Master documents (new)
CREATE TABLE context_master_docs (
  id TEXT PRIMARY KEY,
  context_id TEXT NOT NULL,
  file_id TEXT NOT NULL,
  is_active INTEGER DEFAULT 1,
  created_at TEXT NOT NULL,
  FOREIGN KEY (context_id) REFERENCES contexts (id) ON DELETE CASCADE,
  FOREIGN KEY (file_id) REFERENCES files (id) ON DELETE CASCADE
);
```

#### Data Flow Architecture
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Homepage      │    │   Files Page    │    │   Chat Page     │
│   (Contexts)    │◄──►│   (File Tree)   │◄──►│   (Messages)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────────────────────────────────────────────────────┐
│                    Context Vault Store                         │
│  - contexts[]        - contextFiles[]      - contextChats[]    │
│  - activeContext     - fileTree           - masterDoc         │
└─────────────────────────────────────────────────────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────────────────────────────────────────────────────┐
│                      Database Layer                            │
│  contexts | context_conversations | context_files | files      │
└─────────────────────────────────────────────────────────────────┘
```

### 2. File Tree Structure

#### Implementation Strategy
- **Virtual File System**: Build tree structure from database file records
- **Lazy Loading**: Load file content only when selected
- **Context Filtering**: Show only files belonging to active context
- **Master Document**: Special file that serves as context overview

#### File Tree Component Architecture
```typescript
interface FileTreeNode {
  id: string
  name: string
  type: 'folder' | 'file'
  path: string
  children?: FileTreeNode[]
  fileId?: string
  metadata?: {
    size: number
    modified: string
    fileType: string
  }
}

interface ContextFileTree {
  contextId: string
  rootNodes: FileTreeNode[]
  masterDocId?: string
}
```

### 3. Integration Points

#### Navigation Flow
1. **Homepage → Files**: Click context card → Navigate to `/files/:contextId`
2. **Files → Chat**: Click "Ask about this file" → Create new chat with file context
3. **Chat → Files**: Reference files via @filename → Quick access to file view
4. **History → Context**: Group conversations by context in history view

#### State Management Integration
```typescript
// Extend existing store
interface AppStore {
  // Existing state...
  
  // New context state
  contexts: Context[]
  activeContextId: string | null
  contextFiles: Record<string, FileTreeNode[]>
  selectedFileId: string | null
  
  // New actions
  loadContexts: () => Promise<void>
  createContext: (name: string, description?: string) => Promise<string>
  setActiveContext: (contextId: string) => void
  addFileToContext: (contextId: string, fileId: string) => Promise<void>
  removeFileFromContext: (contextId: string, fileId: string) => Promise<void>
  setMasterDocument: (contextId: string, fileId: string) => Promise<void>
}
```

## File Structure & Components

### New Pages & Components
```
src/
├── pages/
│   ├── HomePage.tsx              # Context cards grid
│   └── FilesPage.tsx             # File tree + preview
├── components/
│   ├── contexts/
│   │   ├── ContextCard.tsx       # Individual context card
│   │   ├── ContextGrid.tsx       # Grid layout with search
│   │   ├── ContextModal.tsx      # 3-panel detail modal
│   │   └── CreateContextModal.tsx # New context creation
│   ├── files/
│   │   ├── FileTree.tsx          # Hierarchical file browser
│   │   ├── FilePreview.tsx       # Markdown/content preview
│   │   ├── QuickActions.tsx      # AI-powered file actions
│   │   └── RecentChats.tsx       # Context-aware chat list
│   └── shared/
│       ├── SearchBar.tsx         # Global search component
│       └── ViewToggle.tsx        # Grid/list view toggle
├── hooks/
│   ├── useContexts.tsx           # Context management
│   ├── useFileTree.tsx           # File tree operations
│   └── useFilePreview.tsx        # File content loading
└── services/
    ├── contextService.ts         # Context CRUD operations
    └── fileTreeService.ts        # File tree building logic
```

### Database Service Extensions
```typescript
// electron/database.ts extensions
class DatabaseManager {
  // New context methods
  async createContext(name: string, description?: string): Promise<string>
  async getContexts(): Promise<Context[]>
  async getContextFiles(contextId: string): Promise<FileRecord[]>
  async getContextConversations(contextId: string): Promise<Conversation[]>
  async addFileToContext(contextId: string, fileId: string): Promise<void>
  async setMasterDocument(contextId: string, fileId: string): Promise<void>
  
  // Enhanced file methods
  async getFilesByContext(contextId: string): Promise<FileRecord[]>
  async searchFilesInContext(contextId: string, query: string): Promise<FileRecord[]>
}
```

## User Stories & Workflows

### Story 1: Project Manager - Organizing Design System Context
**As a** project manager  
**I want to** organize all design system related files and conversations  
**So that** I can quickly access relevant context and track project progress

**Workflow:**
1. Navigate to Homepage → See existing contexts or create new "Design System" context
2. Add files (tokens.json, components.fig, master.md) to context via drag-drop or file picker
3. Set master.md as the master document for context overview
4. Start conversations about specific files → Conversations automatically linked to context
5. View context card showing "12 chats, 24 files" with recent activity indicator

### Story 2: Developer - File-Centric AI Assistance
**As a** developer  
**I want to** get AI insights about specific files in my project  
**So that** I can understand code structure and get implementation suggestions

**Workflow:**
1. Navigate to Files page → Browse file tree for "project-alpha" context
2. Select master.md → Preview appears with markdown rendering
3. Click "Ask about this file" → Opens chat with file context pre-loaded
4. Use quick actions: "Summarize", "Edit content", "Generate documentation"
5. View recent chats related to this file in bottom panel

### Story 3: Knowledge Worker - Context Switching
**As a** knowledge worker  
**I want to** quickly switch between different project contexts  
**So that** I can maintain focus and access relevant information efficiently

**Workflow:**
1. Homepage shows all contexts as cards with visual indicators
2. Search across contexts: "design tokens" → Shows relevant contexts and files
3. Click context card → Opens context detail modal with 3-panel view
4. Navigate between Recent Chats, Files, and Master Document preview
5. Click "View" button → Navigate to full Files page for that context

## Technical Implementation Phases

### Phase 1: Foundation (Week 1-2)
- [ ] Database schema migration for contexts system
- [ ] Basic Context store integration with Zustand
- [ ] Homepage route and basic context grid layout
- [ ] Context creation and management UI

### Phase 2: File Integration (Week 3-4)  
- [ ] File tree component with virtual scrolling
- [ ] File preview with markdown rendering
- [ ] Context-file relationship management
- [ ] Master document designation system

### Phase 3: AI Integration (Week 5-6)
- [ ] Quick actions panel with AI prompts
- [ ] Context-aware chat creation
- [ ] File-to-chat reference system
- [ ] Search across contexts and files

### Phase 4: Polish & Optimization (Week 7-8)
- [ ] Performance optimization for large file trees
- [ ] Advanced search with filters
- [ ] Context import/export functionality
- [ ] Mobile responsive design

## Success Metrics
- **Context Organization**: Users create and maintain 3+ contexts on average
- **File Engagement**: 70% of files accessed through context system vs direct file browser
- **Chat Context**: 60% of new chats initiated from file context
- **Search Usage**: Global search used in 40% of user sessions
- **Navigation Flow**: Smooth transitions between Homepage → Files → Chat with <200ms load times

## Risk Mitigation
- **Database Migration**: Implement backward-compatible schema changes with rollback capability
- **Performance**: Use virtual scrolling and lazy loading for large file trees
- **User Adoption**: Provide migration path from existing conversations to context system
- **Data Integrity**: Ensure context relationships maintain referential integrity during file operations

This implementation transforms ChatLo from a chat-centric application into a comprehensive context vault while preserving all existing functionality and providing clear upgrade paths for current users.

## Detailed Component Specifications

### Homepage Context Cards
```typescript
interface ContextCardProps {
  context: Context
  stats: {
    chatCount: number
    fileCount: number
    lastUpdated: string
    hasArtifacts: boolean
  }
  onSelect: (contextId: string) => void
  onInfo: (contextId: string) => void
}

// Visual Design Mapping:
// - Gradient backgrounds based on context color theme
// - Hover animations with translateY(-1px)
// - Stats display: "12 chats • 24 files"
// - Status indicator dot with color coding
// - Action buttons: View (primary) + Info (secondary)
```

### Files Page Layout
```typescript
interface FilesPageLayout {
  leftPanel: {
    width: '20%'
    component: 'FileTree'
    features: ['hierarchical', 'collapsible', 'search', 'add-file']
  }
  rightPanel: {
    top: {
      height: '60%'
      component: 'FilePreview'
      features: ['markdown-render', 'syntax-highlight', 'quick-actions']
    }
    bottom: {
      height: '40%'
      component: 'RecentChats'
      features: ['context-filtered', 'file-related', 'quick-reply']
    }
  }
}
```

### Context Modal 3-Panel Design
```typescript
interface ContextModalPanels {
  recentChats: {
    width: '30%'
    maxItems: 10
    features: ['click-to-open', 'timestamp', 'message-preview']
  }
  files: {
    width: '30%'
    features: ['file-icons', 'type-indicators', 'quick-preview']
  }
  masterPreview: {
    width: '40%'
    features: ['markdown-render', 'scroll-sync', 'edit-button']
  }
}
```

## Integration with Existing Systems

### Router Updates
```typescript
// src/App.tsx - Add new routes
<Routes>
  <Route path="/" element={<HomePage />} />           {/* NEW */}
  <Route path="/chat" element={<ChatArea />} />       {/* MOVED */}
  <Route path="/chat/:id" element={<ChatArea />} />   {/* EXISTING */}
  <Route path="/files" element={<FilesPage />} />     {/* NEW */}
  <Route path="/files/:contextId" element={<FilesPage />} /> {/* NEW */}
  <Route path="/history" element={<HistoryPage />} />  {/* EXISTING */}
  <Route path="/settings" element={<SettingsPage />} /> {/* EXISTING */}
</Routes>
```

### IconBar Navigation Updates
```typescript
// src/components/IconBar.tsx - Update navigation items
const navigationItems = [
  { name: 'home', path: '/', icon: 'fa-solid fa-home', tooltip: 'Home' },
  { name: 'chat', path: '/chat', icon: 'fa-solid fa-comment', tooltip: 'Chat' },
  { name: 'history', path: '/history', icon: 'fa-solid fa-clock-rotate-left', tooltip: 'History' },
  { name: 'files', path: '/files', icon: 'fa-solid fa-folder-tree', tooltip: 'Files' }
]
```

### Store Integration Pattern
```typescript
// src/store/contextSlice.ts - New store slice
interface ContextSlice {
  // State
  contexts: Context[]
  activeContextId: string | null
  contextFiles: Record<string, FileTreeNode[]>
  selectedFileId: string | null
  searchQuery: string
  viewMode: 'grid' | 'list'

  // Actions
  loadContexts: () => Promise<void>
  createContext: (data: CreateContextData) => Promise<string>
  updateContext: (id: string, updates: Partial<Context>) => Promise<void>
  deleteContext: (id: string) => Promise<void>
  setActiveContext: (id: string | null) => void

  // File operations
  loadContextFiles: (contextId: string) => Promise<void>
  addFileToContext: (contextId: string, fileId: string) => Promise<void>
  removeFileFromContext: (contextId: string, fileId: string) => Promise<void>
  setMasterDocument: (contextId: string, fileId: string) => Promise<void>

  // UI state
  setSearchQuery: (query: string) => void
  setViewMode: (mode: 'grid' | 'list') => void
  setSelectedFile: (fileId: string | null) => void
}
```

## Migration Strategy

### Existing Data Migration
1. **Auto-Context Creation**: Create default contexts for existing conversations
2. **File Association**: Link existing files to appropriate contexts based on usage patterns
3. **Conversation Grouping**: Group related conversations using title similarity and file references
4. **Master Document Detection**: Identify potential master documents (README.md, index.md, etc.)

### Backward Compatibility
- Existing chat URLs remain functional
- Current conversation sidebar preserved as fallback
- File attachment system continues to work
- Settings and preferences maintained

### User Onboarding
1. **Welcome Tour**: Guided introduction to context system
2. **Migration Assistant**: Help users organize existing data
3. **Quick Start Templates**: Pre-configured contexts for common use cases
4. **Progressive Disclosure**: Gradually introduce advanced features

## Performance Considerations

### Optimization Strategies
- **Virtual Scrolling**: Handle large file trees efficiently
- **Lazy Loading**: Load file content and previews on demand
- **Caching**: Cache file tree structures and search results
- **Debounced Search**: Prevent excessive API calls during typing
- **Pagination**: Limit context cards and file lists per page

### Memory Management
- **Component Cleanup**: Proper cleanup of file watchers and subscriptions
- **Image Optimization**: Compress and cache file previews
- **Database Indexing**: Optimize queries for context and file relationships
- **State Pruning**: Remove unused context data from memory

This comprehensive plan provides a clear roadmap for transforming ChatLo into a powerful context vault while maintaining the familiar chat experience users expect.
