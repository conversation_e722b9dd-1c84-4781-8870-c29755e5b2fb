import React, { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from './ChatLoLogo'
import { useNetworkStore } from '../stores/networkStore'

interface AppTopBarProps {
  className?: string
}

const AppTopBar: React.FC<AppTopBarProps> = ({ className = '' }) => {
  const navigate = useNavigate()
  const { isPrivateMode, togglePrivateMode } = useNetworkStore()
  const [currentNewsIndex, setCurrentNewsIndex] = useState(0)

  // Sample news items - this could be fetched from a service
  const newsItems = [
    "New AI model available - DeepSeek V3 Pro",
    "ChatLo v2.1 released with enhanced features",
    "Improved local model support now available"
  ]

  const handlePreviousNews = () => {
    setCurrentNewsIndex((prev) => 
      prev === 0 ? newsItems.length - 1 : prev - 1
    )
  }

  const handleNextNews = () => {
    setCurrentNewsIndex((prev) => 
      prev === newsItems.length - 1 ? 0 : prev + 1
    )
  }

  const handlePrivateModeToggle = () => {
    togglePrivateMode()
  }

  const handleUserProfile = () => {
    // Navigate to user profile or show profile menu
    console.log('User profile clicked')
  }

  const handleSettings = () => {
    navigate('/settings')
  }

  return (
    <div className={`h-12 glass border-b border-white/10 flex items-center px-4 bg-gradient-to-r from-primary/10 to-secondary/10 ${className}`}>
      {/* Logo */}
      <div className="flex items-center gap-2">
        <div className="w-6 h-6 bg-primary/80 rounded flex items-center justify-center glow-primary">
          <i className="fa-solid fa-comment text-gray-900 text-xs"></i>
        </div>
        <span className="text-sm font-semibold text-primary">Chatlo</span>
      </div>
      
      {/* News Update Center */}
      <div className="flex-1 flex items-center justify-center ml-12 mr-[calc(256px-48px)]">
        <div className="flex items-center gap-2 glass-subtle rounded-lg px-3 py-1 w-full max-w-md">
          <button 
            onClick={handlePreviousNews}
            className="p-1 hover:bg-white/10 rounded transition-colors"
          >
            <i className="fa-solid fa-chevron-left text-gray-300 text-xs"></i>
          </button>
          <div className="flex items-center gap-2 flex-1">
            <i className="fa-solid fa-bell text-supplement2 text-xs"></i>
            <span className="text-xs text-supplement1">{newsItems[currentNewsIndex]}</span>
          </div>
          <button 
            onClick={handleNextNews}
            className="p-1 hover:bg-white/10 rounded transition-colors"
          >
            <i className="fa-solid fa-chevron-right text-gray-300 text-xs"></i>
          </button>
        </div>
      </div>
      
      {/* Right Side Controls */}
      <div className="flex items-center gap-3">
        {/* Private Mode Toggle */}
        <div className="flex items-center gap-2">
          <span className="text-xs text-supplement1">Private</span>
          <button 
            onClick={handlePrivateModeToggle}
            className={`relative inline-flex h-4 w-7 items-center rounded-full transition-colors ${
              isPrivateMode
                ? 'bg-secondary/80 glow-secondary'
                : 'bg-gray-600'
            }`}
          >
            <span 
              className={`inline-block h-3 w-3 transform rounded-full bg-white transition-transform ${
                isPrivateMode ? 'translate-x-3.5' : 'translate-x-0.5'
              }`}
            />
          </button>
        </div>
        
        {/* User Icon */}
        <button
          onClick={handleUserProfile}
          className="p-2 hover:bg-white/10 rounded-lg transition-colors group relative"
        >
          <i className="fa-solid fa-user text-supplement1 text-sm"></i>
          <div className="absolute top-12 left-1/2 transform -translate-x-1/2 glass text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap z-10">
            User Profile
          </div>
        </button>

        {/* Settings Icon */}
        <button
          onClick={handleSettings}
          className="p-2 hover:bg-white/10 rounded-lg transition-colors group relative"
        >
          <i className="fa-solid fa-gear text-supplement1 text-sm"></i>
          <div className="absolute top-12 left-1/2 transform -translate-x-1/2 glass text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap z-10">
            Settings
          </div>
        </button>
      </div>
    </div>
  )
}

export default AppTopBar
